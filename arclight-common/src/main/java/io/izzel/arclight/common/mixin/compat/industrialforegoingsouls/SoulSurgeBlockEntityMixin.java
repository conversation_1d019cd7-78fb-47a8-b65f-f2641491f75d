package io.izzel.arclight.common.mixin.compat.industrialforegoingsouls;

import io.izzel.arclight.common.mod.compat.ModIds;
import io.izzel.arclight.common.mod.mixins.annotation.LoadIfMod;
import org.spongepowered.asm.mixin.Mixin;
import org.spongepowered.asm.mixin.Shadow;
import org.spongepowered.asm.mixin.injection.At;
import org.spongepowered.asm.mixin.injection.Inject;
import org.spongepowered.asm.mixin.injection.callback.CallbackInfo;

/**
 * Fix for Industrial Foregoing Souls SoulSurgeBlockEntity NPE
 * Prevents server crash when network is null in serverTick method
 */
@Mixin(targets = "com.buuz135.industrialforegoingsouls.block.tile.SoulSurgeBlockEntity")
@LoadIfMod(modid = {ModIds.INDUSTRIAL_FOREGOING_SOULS}, condition = LoadIfMod.ModCondition.PRESENT)
public class SoulSurgeBlockEntityMixin {

    @Shadow(remap = false)
    private Object network;

    /**
     * Prevent NPE by checking if network is null at the start of serverTick
     * If network is null, skip the entire tick to prevent crash
     */
    @Inject(method = "serverTick", at = @At("HEAD"), cancellable = true, remap = false)
    private void luminara$preventNetworkNPE(CallbackInfo ci) {
        if (this.network == null) {
            ci.cancel();
        }
    }
}
